//! Component system for TUI React
//!
//! This module provides the core component traits and types for building
//! declarative terminal user interfaces.

use ratatui::{Frame, layout::Rect};

/// Trait for all TUI React components
pub trait Component {
    /// The properties type for this component
    type Props;

    /// Create a new instance of the component with props
    fn new(props: Self::Props) -> Self
    where
        Self: Sized;

    /// Render the component to the given frame and area
    fn render(&self, frame: &mut Frame, area: Rect);
}

/// A basic text component
pub struct Text {
    content: String,
}

impl Text {
    /// Create a new text component
    pub fn new(content: impl Into<String>) -> Self {
        Self {
            content: content.into(),
        }
    }
}

impl Component for Text {
    type Props = String;

    fn new(props: Self::Props) -> Self {
        Self { content: props }
    }

    fn render(&self, frame: &mut Frame, area: Rect) {
        use ratatui::widgets::{Paragraph, Widget};
        let paragraph = Paragraph::new(self.content.as_str());
        paragraph.render(area, frame.buffer_mut());
    }
}
