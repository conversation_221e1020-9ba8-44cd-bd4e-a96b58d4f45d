{"rustc": 16591470773350601817, "features": "[\"default\", \"is_variant\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 7053799122731476873, "deps": [[3060637413840920116, "proc_macro2", false, 2559919647406894501], [4974441333307933176, "syn", false, 12316704875031848002], [17685210698997651194, "convert_case", false, 10993303640499094773], [17990358020177143287, "quote", false, 15211608984601574729]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-impl-2d63c083571f5f7d\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}