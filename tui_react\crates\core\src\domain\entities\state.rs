//! State entity for TUI React Core
//!
//! This module defines the state management system for components,
//! providing a type-safe and reactive state container.

use crate::domain::value_objects::ComponentId;
use crate::error::{DomainError, DomainResult};
use std::collections::HashMap;
use std::any::{Any, TypeId};

/// A type-safe state container for components
///
/// The ComponentState follows the Single Responsibility Principle by focusing
/// solely on state storage and change tracking, without coupling to specific
/// component implementations.
#[derive(Debug)]
pub struct ComponentState {
    /// Component this state belongs to
    component_id: ComponentId,
    
    /// Type-erased state values
    values: HashMap<String, Box<dyn Any + Send + Sync>>,
    
    /// Type information for each state value
    type_info: HashMap<String, TypeId>,
    
    /// Change tracking for state values
    changed_keys: Vec<String>,
    
    /// Version counter for optimistic updates
    version: u64,
}

/// A state change event
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct StateChange {
    /// Component that changed
    pub component_id: ComponentId,
    
    /// Key that changed
    pub key: String,
    
    /// Previous version
    pub previous_version: u64,
    
    /// New version
    pub new_version: u64,
}

/// Trait for types that can be stored in component state
pub trait StateValue: Any + Send + Sync + std::fmt::Debug {
    /// Clone the state value
    fn clone_state(&self) -> Box<dyn StateValue>;
    
    /// Check if this value equals another value
    fn equals(&self, other: &dyn StateValue) -> bool;
}

impl<T> StateValue for T 
where 
    T: Any + Send + Sync + std::fmt::Debug + Clone + PartialEq + 'static 
{
    fn clone_state(&self) -> Box<dyn StateValue> {
        Box::new(self.clone())
    }
    
    fn equals(&self, other: &dyn StateValue) -> bool {
        other.downcast_ref::<T>()
            .map(|other_t| self == other_t)
            .unwrap_or(false)
    }
}

impl ComponentState {
    /// Create a new component state
    ///
    /// # Arguments
    /// * `component_id` - ID of the component this state belongs to
    pub fn new(component_id: ComponentId) -> Self {
        Self {
            component_id,
            values: HashMap::new(),
            type_info: HashMap::new(),
            changed_keys: Vec::new(),
            version: 0,
        }
    }

    /// Get the component ID this state belongs to
    pub fn component_id(&self) -> &ComponentId {
        &self.component_id
    }

    /// Get the current version of the state
    pub fn version(&self) -> u64 {
        self.version
    }

    /// Set a state value
    ///
    /// # Arguments
    /// * `key` - State key
    /// * `value` - New state value
    ///
    /// # Type Parameters
    /// * `T` - Type of the state value
    ///
    /// # Returns
    /// StateChange if the value actually changed, None if it's the same
    pub fn set<T>(&mut self, key: impl Into<String>, value: T) -> DomainResult<Option<StateChange>>
    where
        T: StateValue + 'static,
    {
        let key = key.into();
        let type_id = TypeId::of::<T>();
        
        // Check if the value actually changed
        let changed = if let Some(existing) = self.values.get(&key) {
            if let Some(existing_typed) = existing.downcast_ref::<T>() {
                !value.equals(existing_typed)
            } else {
                // Type changed, so it's definitely a change
                true
            }
        } else {
            // New value, so it's a change
            true
        };
        
        if changed {
            let previous_version = self.version;
            self.version += 1;
            
            self.values.insert(key.clone(), Box::new(value));
            self.type_info.insert(key.clone(), type_id);
            
            // Track the change
            if !self.changed_keys.contains(&key) {
                self.changed_keys.push(key.clone());
            }
            
            Ok(Some(StateChange {
                component_id: self.component_id.clone(),
                key,
                previous_version,
                new_version: self.version,
            }))
        } else {
            Ok(None)
        }
    }

    /// Get a state value
    ///
    /// # Arguments
    /// * `key` - State key
    ///
    /// # Type Parameters
    /// * `T` - Expected type of the state value
    ///
    /// # Returns
    /// Reference to the state value if it exists and has the correct type
    pub fn get<T>(&self, key: &str) -> Option<&T>
    where
        T: StateValue + 'static,
    {
        let expected_type = TypeId::of::<T>();
        
        if let Some(&actual_type) = self.type_info.get(key) {
            if actual_type == expected_type {
                self.values.get(key)?.downcast_ref::<T>()
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Get a mutable reference to a state value
    ///
    /// # Arguments
    /// * `key` - State key
    ///
    /// # Type Parameters
    /// * `T` - Expected type of the state value
    ///
    /// # Returns
    /// Mutable reference to the state value if it exists and has the correct type
    pub fn get_mut<T>(&mut self, key: &str) -> Option<&mut T>
    where
        T: StateValue + 'static,
    {
        let expected_type = TypeId::of::<T>();
        
        if let Some(&actual_type) = self.type_info.get(key) {
            if actual_type == expected_type {
                // Mark as changed when getting mutable reference
                if !self.changed_keys.contains(&key.to_string()) {
                    self.changed_keys.push(key.to_string());
                }
                self.version += 1;
                
                self.values.get_mut(key)?.downcast_mut::<T>()
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Check if a state value exists
    ///
    /// # Arguments
    /// * `key` - State key
    pub fn contains(&self, key: &str) -> bool {
        self.values.contains_key(key)
    }

    /// Remove a state value
    ///
    /// # Arguments
    /// * `key` - State key
    ///
    /// # Returns
    /// StateChange if a value was removed, None if the key didn't exist
    pub fn remove(&mut self, key: &str) -> Option<StateChange> {
        if self.values.remove(key).is_some() {
            self.type_info.remove(key);
            
            let previous_version = self.version;
            self.version += 1;
            
            if !self.changed_keys.contains(&key.to_string()) {
                self.changed_keys.push(key.to_string());
            }
            
            Some(StateChange {
                component_id: self.component_id.clone(),
                key: key.to_string(),
                previous_version,
                new_version: self.version,
            })
        } else {
            None
        }
    }

    /// Get all state keys
    pub fn keys(&self) -> impl Iterator<Item = &String> {
        self.values.keys()
    }

    /// Get the number of state values
    pub fn len(&self) -> usize {
        self.values.len()
    }

    /// Check if there are no state values
    pub fn is_empty(&self) -> bool {
        self.values.is_empty()
    }

    /// Get all changed keys since last reset
    pub fn changed_keys(&self) -> &[String] {
        &self.changed_keys
    }

    /// Check if any state has changed since last reset
    pub fn has_changes(&self) -> bool {
        !self.changed_keys.is_empty()
    }

    /// Reset change tracking
    ///
    /// Clears the list of changed keys, typically called after processing changes
    pub fn reset_changes(&mut self) {
        self.changed_keys.clear();
    }

    /// Create a snapshot of the current state
    ///
    /// Returns a cloned version of all state values for backup/restore purposes
    pub fn snapshot(&self) -> HashMap<String, Box<dyn StateValue>> {
        let mut snapshot = HashMap::new();
        
        for (key, value) in &self.values {
            if let Some(state_value) = value.downcast_ref::<Box<dyn StateValue>>() {
                snapshot.insert(key.clone(), state_value.clone_state());
            }
        }
        
        snapshot
    }

    /// Restore state from a snapshot
    ///
    /// # Arguments
    /// * `snapshot` - Previously created snapshot to restore from
    pub fn restore_snapshot(&mut self, snapshot: HashMap<String, Box<dyn StateValue>>) -> DomainResult<Vec<StateChange>> {
        let mut changes = Vec::new();
        let previous_version = self.version;
        
        // Clear current state
        self.values.clear();
        self.type_info.clear();
        self.changed_keys.clear();
        
        // Restore from snapshot
        for (key, value) in snapshot {
            // Note: This is simplified - in a real implementation, you'd need
            // to properly handle the type information restoration
            self.values.insert(key.clone(), value);
            self.changed_keys.push(key.clone());
            
            changes.push(StateChange {
                component_id: self.component_id.clone(),
                key,
                previous_version,
                new_version: self.version + 1,
            });
        }
        
        self.version += 1;
        
        Ok(changes)
    }
}

impl Clone for ComponentState {
    fn clone(&self) -> Self {
        let mut cloned = ComponentState::new(self.component_id.clone());
        cloned.version = self.version;
        cloned.changed_keys = self.changed_keys.clone();
        cloned.type_info = self.type_info.clone();
        
        // Clone state values using the StateValue trait
        for (key, value) in &self.values {
            if let Some(state_value) = value.downcast_ref::<Box<dyn StateValue>>() {
                cloned.values.insert(key.clone(), state_value.clone_state());
            }
        }
        
        cloned
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_state_basic_operations() {
        let component_id = ComponentId::generate();
        let mut state = ComponentState::new(component_id.clone());
        
        // Set state values
        let change1 = state.set("count", 42i32).unwrap();
        assert!(change1.is_some());
        
        let change2 = state.set("title", "Hello".to_string()).unwrap();
        assert!(change2.is_some());
        
        // Get state values
        assert_eq!(state.get::<i32>("count"), Some(&42));
        assert_eq!(state.get::<String>("title"), Some(&"Hello".to_string()));
        
        // Check changes
        assert!(state.has_changes());
        assert_eq!(state.changed_keys().len(), 2);
    }

    #[test]
    fn test_state_change_detection() {
        let component_id = ComponentId::generate();
        let mut state = ComponentState::new(component_id);
        
        // Set initial value
        let change1 = state.set("value", 10i32).unwrap();
        assert!(change1.is_some());
        
        // Set same value - should not create change
        let change2 = state.set("value", 10i32).unwrap();
        assert!(change2.is_none());
        
        // Set different value - should create change
        let change3 = state.set("value", 20i32).unwrap();
        assert!(change3.is_some());
    }

    #[test]
    fn test_state_type_safety() {
        let component_id = ComponentId::generate();
        let mut state = ComponentState::new(component_id);
        
        state.set("value", 42i32).unwrap();
        
        // Correct type
        assert_eq!(state.get::<i32>("value"), Some(&42));
        
        // Wrong type
        assert_eq!(state.get::<String>("value"), None);
    }

    #[test]
    fn test_state_versioning() {
        let component_id = ComponentId::generate();
        let mut state = ComponentState::new(component_id);
        
        assert_eq!(state.version(), 0);
        
        state.set("value", 1i32).unwrap();
        assert_eq!(state.version(), 1);
        
        state.set("value", 2i32).unwrap();
        assert_eq!(state.version(), 2);
        
        // Same value shouldn't increment version
        state.set("value", 2i32).unwrap();
        assert_eq!(state.version(), 2);
    }
}
