//! Domain layer for TUI React Core
//!
//! This module contains the core business entities, value objects, and domain services
//! that represent the fundamental concepts of the TUI React system.
//!
//! The domain layer is the innermost layer of Clean Architecture and contains:
//! - Entities: Core business objects with identity and behavior
//! - Value Objects: Immutable objects that describe aspects of the domain
//! - Domain Services: Business logic that doesn't naturally fit in entities

pub mod entities;
pub mod services;
pub mod value_objects;

// Re-export public types
pub use entities::*;
pub use services::*;
pub use value_objects::*;
