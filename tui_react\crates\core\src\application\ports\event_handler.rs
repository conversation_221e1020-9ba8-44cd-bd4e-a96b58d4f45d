//! Event handler port
//!
//! This module defines the interface for handling events in the TUI React system,
//! following the Dependency Inversion Principle.

/// Event type enumeration
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Event {
    /// Key press event
    KeyPress(KeyEvent),
    /// Mouse event
    Mouse(MouseEvent),
    /// Resize event
    Resize { width: u16, height: u16 },
    /// Custom application event
    Custom(String),
}

/// Key event details
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct KeyEvent {
    /// The key that was pressed
    pub key: Key,
    /// Modifier keys that were held
    pub modifiers: KeyModifiers,
}

/// Key enumeration
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Key {
    /// Character key
    Char(char),
    /// Enter key
    Enter,
    /// Escape key
    Escape,
    /// Tab key
    Tab,
    /// Arrow keys
    Up,
    /// Down arrow
    Down,
    /// Left arrow
    Left,
    /// Right arrow
    Right,
    /// Function keys
    F(u8),
}

/// Key modifiers
#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub struct KeyModifiers {
    /// Control key
    pub ctrl: bool,
    /// Alt key
    pub alt: bool,
    /// Shift key
    pub shift: bool,
}

/// Mouse event details
#[derive(Debug, Clone, PartialEq, Eq)]
pub struct MouseEvent {
    /// Mouse event type
    pub event_type: MouseEventType,
    /// X coordinate
    pub x: u16,
    /// Y coordinate
    pub y: u16,
}

/// Mouse event type
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum MouseEventType {
    /// Mouse button press
    Press(MouseButton),
    /// Mouse button release
    Release(MouseButton),
    /// Mouse movement
    Move,
    /// Mouse scroll
    Scroll(ScrollDirection),
}

/// Mouse button enumeration
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum MouseButton {
    /// Left mouse button
    Left,
    /// Right mouse button
    Right,
    /// Middle mouse button
    Middle,
}

/// Scroll direction
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ScrollDirection {
    /// Scroll up
    Up,
    /// Scroll down
    Down,
}

/// Event handler trait
/// 
/// This trait defines the interface for handling events in the application.
/// It follows the Dependency Inversion Principle by allowing different
/// event handling implementations.
pub trait EventHandler {
    /// Handle an event
    fn handle_event(&mut self, event: Event) -> Result<(), EventHandlerError>;
    
    /// Check if the handler can handle a specific event type
    fn can_handle(&self, event: &Event) -> bool;
}

/// Error type for event handling operations
#[derive(Debug, Clone)]
pub enum EventHandlerError {
    /// Event not supported
    NotSupported(String),
    /// Event handling failed
    HandlingFailed(String),
}

impl std::fmt::Display for EventHandlerError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::NotSupported(msg) => write!(f, "Event not supported: {}", msg),
            Self::HandlingFailed(msg) => write!(f, "Event handling failed: {}", msg),
        }
    }
}

impl std::error::Error for EventHandlerError {}
