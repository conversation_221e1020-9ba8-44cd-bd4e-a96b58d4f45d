{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 12410652206962508598, "path": 14468743889151885865, "deps": [[1988483478007900009, "unicode_ident", false, 6801611108999218727], [3060637413840920116, "proc_macro2", false, 7930516510063098723], [17990358020177143287, "quote", false, 10336293893624943732]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-dc991ec0dd4641d4\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}