[package]
name = "tui_react_core"
version = "0.1.0"
edition = "2024"

[dependencies]
# Core dependencies
ratatui = { workspace = true }
crossterm = { workspace = true }

# Proc macros
core_macros = { workspace = true }

# Error handling
thiserror = { workspace = true }
anyhow = { workspace = true }

# Collections and utilities
indexmap = { workspace = true }
fastrand = "2.0"

# Serialization
serde_json = { workspace = true }

# Optional async support
tokio = { workspace = true, optional = true }

[dev-dependencies]
trybuild = { workspace = true }

[features]
default = []
async = ["tokio"]
