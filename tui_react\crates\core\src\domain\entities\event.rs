//! Event entity for TUI React Core
//!
//! This module defines the event system for handling user interactions
//! and system events in a type-safe manner.

use crate::domain::value_objects::ComponentId;
use std::time::{SystemTime, UNIX_EPOCH};

/// A domain event in the TUI React system
///
/// Events represent things that have happened in the system and follow
/// the Single Responsibility Principle by focusing solely on event data
/// without coupling to specific handling mechanisms.
#[derive(Debug, Clone, PartialEq)]
pub struct DomainEvent {
    /// Unique identifier for this event
    id: EventId,
    
    /// Type of the event
    event_type: EventType,
    
    /// Component that generated or is the target of this event
    component_id: Option<ComponentId>,
    
    /// Timestamp when the event occurred
    timestamp: u64,
    
    /// Event payload data
    payload: EventPayload,
    
    /// Whether this event has been handled
    handled: bool,
    
    /// Whether this event should bubble up the component tree
    bubbles: bool,
    
    /// Whether this event can be cancelled
    cancelable: bool,
    
    /// Whether this event has been cancelled
    cancelled: bool,
}

/// Unique identifier for an event
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, <PERSON>q, <PERSON>h)]
pub struct EventId {
    value: String,
}

/// Types of events that can occur in the system
#[derive(Debug, Clone, PartialEq)]
pub enum EventType {
    /// User input events
    Input(InputEventType),
    
    /// Component lifecycle events
    Lifecycle(LifecycleEventType),
    
    /// State change events
    StateChange(StateChangeEventType),
    
    /// Rendering events
    Render(RenderEventType),
    
    /// Custom application events
    Custom(String),
}

/// Types of user input events
#[derive(Debug, Clone, PartialEq)]
pub enum InputEventType {
    /// Key press event
    KeyPress,
    
    /// Key release event
    KeyRelease,
    
    /// Mouse click event
    MouseClick,
    
    /// Mouse move event
    MouseMove,
    
    /// Mouse scroll event
    MouseScroll,
    
    /// Focus gained event
    FocusGained,
    
    /// Focus lost event
    FocusLost,
}

/// Types of component lifecycle events
#[derive(Debug, Clone, PartialEq)]
pub enum LifecycleEventType {
    /// Component was created
    Created,
    
    /// Component was initialized
    Initialized,
    
    /// Component was mounted
    Mounted,
    
    /// Component is being updated
    Updating,
    
    /// Component update completed
    Updated,
    
    /// Component is being unmounted
    Unmounting,
    
    /// Component was unmounted
    Unmounted,
    
    /// Component was destroyed
    Destroyed,
}

/// Types of state change events
#[derive(Debug, Clone, PartialEq)]
pub enum StateChangeEventType {
    /// State value was set
    ValueSet,
    
    /// State value was removed
    ValueRemoved,
    
    /// State was reset
    StateReset,
}

/// Types of rendering events
#[derive(Debug, Clone, PartialEq)]
pub enum RenderEventType {
    /// Render started
    RenderStarted,
    
    /// Render completed
    RenderCompleted,
    
    /// Render failed
    RenderFailed,
    
    /// Layout calculated
    LayoutCalculated,
}

/// Event payload containing event-specific data
#[derive(Debug, Clone, PartialEq)]
pub enum EventPayload {
    /// No payload data
    None,
    
    /// String payload
    String(String),
    
    /// Integer payload
    Integer(i64),
    
    /// Boolean payload
    Boolean(bool),
    
    /// Key event payload
    Key {
        key: String,
        modifiers: Vec<String>,
    },
    
    /// Mouse event payload
    Mouse {
        x: u16,
        y: u16,
        button: Option<String>,
    },
    
    /// State change payload
    StateChange {
        key: String,
        old_version: u64,
        new_version: u64,
    },
    
    /// Error payload
    Error {
        message: String,
        code: Option<String>,
    },
    
    /// Custom payload
    Custom(serde_json::Value),
}

impl EventId {
    /// Generate a new unique event ID
    pub fn generate() -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos();
            
        let random = fastrand::u64(..);
        
        Self {
            value: format!("event_{}_{}", timestamp, random),
        }
    }

    /// Create an event ID from a string
    pub fn from_string(value: String) -> Self {
        Self { value }
    }

    /// Get the string value of the ID
    pub fn value(&self) -> &str {
        &self.value
    }
}

impl DomainEvent {
    /// Create a new domain event
    ///
    /// # Arguments
    /// * `event_type` - Type of the event
    /// * `component_id` - Optional component ID associated with the event
    /// * `payload` - Event payload data
    pub fn new(
        event_type: EventType,
        component_id: Option<ComponentId>,
        payload: EventPayload,
    ) -> Self {
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            id: EventId::generate(),
            event_type,
            component_id,
            timestamp,
            payload,
            handled: false,
            bubbles: true,
            cancelable: true,
            cancelled: false,
        }
    }

    /// Create a key press event
    pub fn key_press(
        component_id: Option<ComponentId>,
        key: String,
        modifiers: Vec<String>,
    ) -> Self {
        Self::new(
            EventType::Input(InputEventType::KeyPress),
            component_id,
            EventPayload::Key { key, modifiers },
        )
    }

    /// Create a mouse click event
    pub fn mouse_click(
        component_id: Option<ComponentId>,
        x: u16,
        y: u16,
        button: Option<String>,
    ) -> Self {
        Self::new(
            EventType::Input(InputEventType::MouseClick),
            component_id,
            EventPayload::Mouse { x, y, button },
        )
    }

    /// Create a component lifecycle event
    pub fn lifecycle(
        component_id: ComponentId,
        lifecycle_type: LifecycleEventType,
    ) -> Self {
        Self::new(
            EventType::Lifecycle(lifecycle_type),
            Some(component_id),
            EventPayload::None,
        )
    }

    /// Create a state change event
    pub fn state_change(
        component_id: ComponentId,
        key: String,
        old_version: u64,
        new_version: u64,
    ) -> Self {
        Self::new(
            EventType::StateChange(StateChangeEventType::ValueSet),
            Some(component_id),
            EventPayload::StateChange {
                key,
                old_version,
                new_version,
            },
        )
    }

    /// Create a custom event
    pub fn custom(
        event_name: String,
        component_id: Option<ComponentId>,
        payload: EventPayload,
    ) -> Self {
        Self::new(
            EventType::Custom(event_name),
            component_id,
            payload,
        )
    }

    /// Get the event ID
    pub fn id(&self) -> &EventId {
        &self.id
    }

    /// Get the event type
    pub fn event_type(&self) -> &EventType {
        &self.event_type
    }

    /// Get the component ID associated with this event
    pub fn component_id(&self) -> Option<&ComponentId> {
        self.component_id.as_ref()
    }

    /// Get the event timestamp
    pub fn timestamp(&self) -> u64 {
        self.timestamp
    }

    /// Get the event payload
    pub fn payload(&self) -> &EventPayload {
        &self.payload
    }

    /// Check if the event has been handled
    pub fn is_handled(&self) -> bool {
        self.handled
    }

    /// Mark the event as handled
    pub fn mark_handled(&mut self) {
        self.handled = true;
    }

    /// Check if the event bubbles
    pub fn bubbles(&self) -> bool {
        self.bubbles
    }

    /// Set whether the event bubbles
    pub fn set_bubbles(&mut self, bubbles: bool) {
        self.bubbles = bubbles;
    }

    /// Check if the event is cancelable
    pub fn is_cancelable(&self) -> bool {
        self.cancelable
    }

    /// Check if the event has been cancelled
    pub fn is_cancelled(&self) -> bool {
        self.cancelled
    }

    /// Cancel the event (if cancelable)
    pub fn cancel(&mut self) -> bool {
        if self.cancelable {
            self.cancelled = true;
            true
        } else {
            false
        }
    }

    /// Stop event propagation
    pub fn stop_propagation(&mut self) {
        self.bubbles = false;
    }

    /// Check if this is an input event
    pub fn is_input_event(&self) -> bool {
        matches!(self.event_type, EventType::Input(_))
    }

    /// Check if this is a lifecycle event
    pub fn is_lifecycle_event(&self) -> bool {
        matches!(self.event_type, EventType::Lifecycle(_))
    }

    /// Check if this is a state change event
    pub fn is_state_change_event(&self) -> bool {
        matches!(self.event_type, EventType::StateChange(_))
    }

    /// Check if this is a render event
    pub fn is_render_event(&self) -> bool {
        matches!(self.event_type, EventType::Render(_))
    }

    /// Check if this is a custom event
    pub fn is_custom_event(&self) -> bool {
        matches!(self.event_type, EventType::Custom(_))
    }
}

impl std::fmt::Display for EventId {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.value)
    }
}

impl std::fmt::Display for EventType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            EventType::Input(input_type) => write!(f, "Input::{:?}", input_type),
            EventType::Lifecycle(lifecycle_type) => write!(f, "Lifecycle::{:?}", lifecycle_type),
            EventType::StateChange(state_type) => write!(f, "StateChange::{:?}", state_type),
            EventType::Render(render_type) => write!(f, "Render::{:?}", render_type),
            EventType::Custom(name) => write!(f, "Custom::{}", name),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_event_creation() {
        let component_id = ComponentId::generate();
        let event = DomainEvent::key_press(
            Some(component_id.clone()),
            "Enter".to_string(),
            vec!["Ctrl".to_string()],
        );

        assert!(event.is_input_event());
        assert_eq!(event.component_id(), Some(&component_id));
        assert!(!event.is_handled());
        assert!(event.bubbles());
        assert!(event.is_cancelable());
        assert!(!event.is_cancelled());
    }

    #[test]
    fn test_event_handling() {
        let mut event = DomainEvent::custom(
            "test_event".to_string(),
            None,
            EventPayload::String("test".to_string()),
        );

        assert!(!event.is_handled());
        event.mark_handled();
        assert!(event.is_handled());
    }

    #[test]
    fn test_event_cancellation() {
        let mut event = DomainEvent::mouse_click(
            None,
            100,
            200,
            Some("left".to_string()),
        );

        assert!(!event.is_cancelled());
        assert!(event.cancel());
        assert!(event.is_cancelled());
    }

    #[test]
    fn test_event_propagation() {
        let mut event = DomainEvent::lifecycle(
            ComponentId::generate(),
            LifecycleEventType::Mounted,
        );

        assert!(event.bubbles());
        event.stop_propagation();
        assert!(!event.bubbles());
    }
}
