{"rustc": 16591470773350601817, "features": "[\"default\", \"is_variant\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 7053799122731476873, "deps": [[3060637413840920116, "proc_macro2", false, 5226982760538691167], [4974441333307933176, "syn", false, 5143749889036807211], [17685210698997651194, "convert_case", false, 5937853584896114228], [17990358020177143287, "quote", false, 5680229744654062707]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-impl-1040358af084fd9b\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}