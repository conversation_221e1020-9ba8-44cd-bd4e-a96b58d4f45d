---
type: "agent_requested"
description: "Example description"
---

Always design code using Clean Architecture and the SOLID principles:

1. **Separate code into layers**:

   - `Domain`: Pure business logic (entities, use cases).
   - `Application`: Orchestrates domain logic and defines interfaces.
   - `Interface/Adapters`: I/O handlers (UI, CLI, DB, web).
   - `Infrastructure`: Frameworks and system dependencies.

2. **Follow SOLID principles**:

   - **S**ingle Responsibility: Every module/class/function should do one thing.
   - **O**pen/Closed: Components should be open for extension, closed for modification.
   - **L**iskov Substitution: Derived types must respect the behavior of base types.
   - **I**nterface Segregation: Prefer small, focused interfaces over large general ones.
   - **D**ependency Inversion: Depend on abstractions, not concretions.

3. **Architectural rules**:

   - Inner layers **must not depend on** outer layers (e.g., domain doesn’t know about CLI/DB).
   - Define **interfaces** (traits/abstractions) in the inner layers, implement them in the outer ones.
   - Prefer **composition over inheritance** and **immutable state** when possible.
   - Ensure **testability** and **loose coupling** by designing components for injection.

4. **Code Output Expectations**:
   - Organize code in folders like `domain/`, `use_cases/`, `infrastructure/`, `presentation/`.
   - Use interfaces and dependency injection for external resources (e.g., DB, terminal, web).
   - Avoid hard-coded logic in UI or system layer — delegate to the domain.
   - Output code that’s **maintainable, extensible, and modular**.

💡 Always prioritize clarity, correctness, and reusability.
