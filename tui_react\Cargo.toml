[workspace]
resolver = "2"
members = ["crates/*"]

[workspace.dependencies]

# Local crates
core_macros = { path = "crates/core_macros" }
tui_react = { path = "crates/tui_react" }
rsx_macro = { path = "crates/rsx_macro" }
tui_react_core = { path = "crates/core" }

# Core dependencies
crossterm = "0.29"
chrono = "0.4"
reqwest = { version = "0.12" }

# Async runtime
tokio = { version = "1", features = ["full"] }

# Logging and tracing
tracing = "0.1"
tracing-futures = "0.2"
tracing-subscriber = { version = "0.3", default-features = false }
tracing-appender = "0.2"

# Panic handling
better-panic = "0.3"
human-panic = "2"

# Serialization
serde = { version = "1" }
serde_json = "1"

# Error handling
thiserror = "2.0"
anyhow = "1.0"

# Proc macro dependencies
proc-macro2 = "1"
proc-macro2-diagnostics = "0.10"
quote = "1"
syn = "2"
syn-rsx = "0.9.0"

# Collections and utilities
indexmap = "2.0"
smallvec = "1.0"
ahash = "0.8"
once_cell = "1.0"

# Development dependencies
typed-builder = "0.21"
paste = "1.0"

# Testing
trybuild = "1"
proptest = "1.0"

# Optional features that can be enabled
[workspace.dependencies.ratatui]
version = "0.29"
default-features = false
features = ["crossterm"]

[workspace.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]
