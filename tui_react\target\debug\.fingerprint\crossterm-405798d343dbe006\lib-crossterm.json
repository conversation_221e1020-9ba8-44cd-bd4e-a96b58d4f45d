{"rustc": 16591470773350601817, "features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"events\", \"windows\"]", "declared_features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"event-stream\", \"events\", \"filedescriptor\", \"libc\", \"osc52\", \"serde\", \"use-dev-tty\", \"windows\"]", "target": 7162149947039624270, "profile": 2241668132362809309, "path": 5746503555594741356, "deps": [[4495526598637097934, "parking_lot", false, 9246956626233736800], [7896293946984509699, "bitflags", false, 6691774172488787065], [10020888071089587331, "<PERSON>ap<PERSON>", false, 18278311270765633969], [11293676373856528358, "derive_more", false, 11818455552624866084], [11763018104473073732, "document_features", false, 13927845093918081132], [17658759660230624279, "crossterm_winapi", false, 6568008406566820428]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crossterm-405798d343dbe006\\dep-lib-crossterm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}