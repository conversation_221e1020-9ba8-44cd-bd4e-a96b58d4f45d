{"rustc": 16591470773350601817, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 14468743889151885865, "deps": [[1988483478007900009, "unicode_ident", false, 6874036977856085267], [3060637413840920116, "proc_macro2", false, 5226982760538691167], [17990358020177143287, "quote", false, 5680229744654062707]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-0ed46df327f776a2\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}