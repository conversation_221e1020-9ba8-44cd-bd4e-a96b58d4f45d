{"$message_type":"diagnostic","message":"file not found for module `application`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":777,"byte_end":797,"line_start":22,"line_end":22,"column_start":1,"column_end":21,"is_primary":true,"text":[{"text":"pub mod application;","highlight_start":1,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `application`, create file \"crates\\core\\src\\application.rs\" or \"crates\\core\\src\\application\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod application` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `application`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:22:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod application;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `application`, create file \"crates\\core\\src\\application.rs\" or \"crates\\core\\src\\application\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod application` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `props`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":210,"byte_end":224,"line_start":7,"line_end":7,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod props;","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `props`, create file \"crates\\core\\src\\domain\\entities\\props.rs\" or \"crates\\core\\src\\domain\\entities\\props\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod props` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `props`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\mod.rs:7:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod props;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `props`, create file \"crates\\core\\src\\domain\\entities\\props.rs\" or \"crates\\core\\src\\domain\\entities\\props\\mod.rs\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod props` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `state`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":225,"byte_end":239,"line_start":8,"line_end":8,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod state;","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `state`, create file \"crates\\core\\src\\domain\\entities\\state.rs\" or \"crates\\core\\src\\domain\\entities\\state\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod state` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `state`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\mod.rs:8:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod state;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `state`, create file \"crates\\core\\src\\domain\\entities\\state.rs\" or \"crates\\core\\src\\domain\\entities\\state\\mod.rs\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod state` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `event`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":240,"byte_end":254,"line_start":9,"line_end":9,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod event;","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `event`, create file \"crates\\core\\src\\domain\\entities\\event.rs\" or \"crates\\core\\src\\domain\\entities\\event\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod event` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `event`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\mod.rs:9:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod event;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `event`, create file \"crates\\core\\src\\domain\\entities\\event.rs\" or \"crates\\core\\src\\domain\\entities\\event\\mod.rs\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod event` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `component_type`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":246,"byte_end":269,"line_start":8,"line_end":8,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"pub mod component_type;","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `component_type`, create file \"crates\\core\\src\\domain\\value_objects\\component_type.rs\" or \"crates\\core\\src\\domain\\value_objects\\component_type\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod component_type` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `component_type`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:8:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod component_type;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `component_type`, create file \"crates\\core\\src\\domain\\value_objects\\component_type.rs\" or \"crates\\core\\src\\domain\\value_objects\\component_type\\mod.rs\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod component_type` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `style`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":270,"byte_end":284,"line_start":9,"line_end":9,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod style;","highlight_start":1,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `style`, create file \"crates\\core\\src\\domain\\value_objects\\style.rs\" or \"crates\\core\\src\\domain\\value_objects\\style\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod style` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `style`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:9:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod style;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `style`, create file \"crates\\core\\src\\domain\\value_objects\\style.rs\" or \"crates\\core\\src\\domain\\value_objects\\style\\mod.rs\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod style` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `layout`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":285,"byte_end":300,"line_start":10,"line_end":10,"column_start":1,"column_end":16,"is_primary":true,"text":[{"text":"pub mod layout;","highlight_start":1,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `layout`, create file \"crates\\core\\src\\domain\\value_objects\\layout.rs\" or \"crates\\core\\src\\domain\\value_objects\\layout\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod layout` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `layout`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:10:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod layout;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `layout`, create file \"crates\\core\\src\\domain\\value_objects\\layout.rs\" or \"crates\\core\\src\\domain\\value_objects\\layout\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod layout` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `dimensions`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":301,"byte_end":320,"line_start":11,"line_end":11,"column_start":1,"column_end":20,"is_primary":true,"text":[{"text":"pub mod dimensions;","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `dimensions`, create file \"crates\\core\\src\\domain\\value_objects\\dimensions.rs\" or \"crates\\core\\src\\domain\\value_objects\\dimensions\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod dimensions` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `dimensions`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:11:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod dimensions;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `dimensions`, create file \"crates\\core\\src\\domain\\value_objects\\dimensions.rs\" or \"crates\\core\\src\\domain\\value_objects\\dimensions\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod dimensions` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `services`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":587,"byte_end":604,"line_start":14,"line_end":14,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"pub mod services;","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `services`, create file \"crates\\core\\src\\domain\\services.rs\" or \"crates\\core\\src\\domain\\services\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod services` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `services`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:14:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod services;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `services`, create file \"crates\\core\\src\\domain\\services.rs\" or \"crates\\core\\src\\domain\\services\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod services` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `repositories`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":605,"byte_end":626,"line_start":15,"line_end":15,"column_start":1,"column_end":22,"is_primary":true,"text":[{"text":"pub mod repositories;","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `repositories`, create file \"crates\\core\\src\\domain\\repositories.rs\" or \"crates\\core\\src\\domain\\repositories\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod repositories` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `repositories`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:15:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod repositories;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `repositories`, create file \"crates\\core\\src\\domain\\repositories.rs\" or \"crates\\core\\src\\domain\\repositories\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod repositories` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `infrastructure`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":816,"byte_end":839,"line_start":24,"line_end":24,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"pub mod infrastructure;","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `infrastructure`, create file \"crates\\core\\src\\infrastructure.rs\" or \"crates\\core\\src\\infrastructure\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod infrastructure` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `infrastructure`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:24:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod infrastructure;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `infrastructure`, create file \"crates\\core\\src\\infrastructure.rs\" or \"crates\\core\\src\\infrastructure\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod infrastructure` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file not found for module `presentation`","code":{"code":"E0583","explanation":"A file wasn't found for an out-of-line module.\n\nErroneous code example:\n\n```compile_fail,E0583\nmod file_that_doesnt_exist; // error: file not found for module\n\nfn main() {}\n```\n\nPlease be sure that a file corresponding to the module exists. If you\nwant to use a module named `file_that_doesnt_exist`, you need to have a file\nnamed `file_that_doesnt_exist.rs` or `file_that_doesnt_exist/mod.rs` in the\nsame directory.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":841,"byte_end":862,"line_start":25,"line_end":25,"column_start":1,"column_end":22,"is_primary":true,"text":[{"text":"pub mod presentation;","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"to create the module `presentation`, create file \"crates\\core\\src\\presentation.rs\" or \"crates\\core\\src\\presentation\\mod.rs\"","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"if there is a `mod presentation` elsewhere in the crate already, import it with `use crate::...` instead","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0583]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file not found for module `presentation`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:25:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod presentation;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: to create the module `presentation`, create file \"crates\\core\\src\\presentation.rs\" or \"crates\\core\\src\\presentation\\mod.rs\"\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: if there is a `mod presentation` elsewhere in the crate already, import it with `use crate::...` instead\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `crate::domain::value_objects::ComponentType`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\component.rs","byte_start":339,"byte_end":352,"line_start":7,"line_end":7,"column_start":49,"column_end":62,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{ComponentId, ComponentType};","highlight_start":49,"highlight_end":62}],"label":"no `ComponentType` in `domain::value_objects`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a similar name exists in the module","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\component.rs","byte_start":339,"byte_end":352,"line_start":7,"line_end":7,"column_start":49,"column_end":62,"is_primary":true,"text":[{"text":"use crate::domain::value_objects::{ComponentId, ComponentType};","highlight_start":49,"highlight_end":62}],"label":null,"suggested_replacement":"component_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `crate::domain::value_objects::ComponentType`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\component.rs:7:49\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::domain::value_objects::{ComponentId, ComponentType};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `ComponentType` in `domain::value_objects`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mhelp: a similar name exists in the module: `component_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `props::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":312,"byte_end":320,"line_start":13,"line_end":13,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"pub use props::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":304,"byte_end":322,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use props::*;","highlight_start":1,"highlight_end":18},{"text":"pub use state::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `props::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\mod.rs:13:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use props::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `state::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":330,"byte_end":338,"line_start":14,"line_end":14,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"pub use state::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":322,"byte_end":340,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use state::*;","highlight_start":1,"highlight_end":18},{"text":"pub use event::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `state::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\mod.rs:14:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use state::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `event::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":348,"byte_end":356,"line_start":15,"line_end":15,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"pub use event::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":340,"byte_end":358,"line_start":15,"line_end":15,"column_start":1,"column_end":19,"is_primary":true,"text":[{"text":"pub use event::*;","highlight_start":1,"highlight_end":19}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `event::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\mod.rs:15:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use event::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `component_type::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":381,"byte_end":398,"line_start":15,"line_end":15,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"pub use component_type::*;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":373,"byte_end":400,"line_start":15,"line_end":16,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use component_type::*;","highlight_start":1,"highlight_end":27},{"text":"pub use style::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `component_type::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:15:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use component_type::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `style::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":408,"byte_end":416,"line_start":16,"line_end":16,"column_start":9,"column_end":17,"is_primary":true,"text":[{"text":"pub use style::*;","highlight_start":9,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":400,"byte_end":418,"line_start":16,"line_end":17,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use style::*;","highlight_start":1,"highlight_end":18},{"text":"pub use layout::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `style::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:16:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use style::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `layout::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":426,"byte_end":435,"line_start":17,"line_end":17,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"pub use layout::*;","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":418,"byte_end":437,"line_start":17,"line_end":18,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use layout::*;","highlight_start":1,"highlight_end":19},{"text":"pub use dimensions::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `layout::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:17:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use layout::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `dimensions::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":445,"byte_end":458,"line_start":18,"line_end":18,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"pub use dimensions::*;","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":437,"byte_end":460,"line_start":18,"line_end":18,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"pub use dimensions::*;","highlight_start":1,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `dimensions::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:18:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use dimensions::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `services::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":709,"byte_end":720,"line_start":20,"line_end":20,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"pub use services::*;","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":701,"byte_end":722,"line_start":20,"line_end":21,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use services::*;","highlight_start":1,"highlight_end":21},{"text":"pub use repositories::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `services::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:20:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use services::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `repositories::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":730,"byte_end":745,"line_start":21,"line_end":21,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"pub use repositories::*;","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":722,"byte_end":747,"line_start":21,"line_end":21,"column_start":1,"column_end":26,"is_primary":true,"text":[{"text":"pub use repositories::*;","highlight_start":1,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `repositories::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:21:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use repositories::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `application::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1025,"byte_end":1039,"line_start":36,"line_end":36,"column_start":9,"column_end":23,"is_primary":true,"text":[{"text":"pub use application::*;","highlight_start":9,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1017,"byte_end":1042,"line_start":36,"line_end":37,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use application::*;","highlight_start":1,"highlight_end":24},{"text":"pub use domain::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `application::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:36:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use application::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `infrastructure::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1089,"byte_end":1106,"line_start":39,"line_end":39,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"pub use infrastructure::*;","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1081,"byte_end":1109,"line_start":39,"line_end":40,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use infrastructure::*;","highlight_start":1,"highlight_end":27},{"text":"pub use presentation::*;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `infrastructure::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:39:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use infrastructure::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `presentation::*`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1117,"byte_end":1132,"line_start":40,"line_end":40,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"pub use presentation::*;","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":1109,"byte_end":1135,"line_start":40,"line_end":41,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use presentation::*;","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `presentation::*`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:40:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m40\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use presentation::*;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"file is loaded as a module multiple times: `crates\\core\\src\\lib.rs`","code":{"code":"clippy::duplicate_mod","explanation":null},"level":"error","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":777,"byte_end":797,"line_start":22,"line_end":22,"column_start":1,"column_end":21,"is_primary":true,"text":[{"text":"pub mod application;","highlight_start":1,"highlight_end":21}],"label":"first loaded here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":210,"byte_end":224,"line_start":7,"line_end":7,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod props;","highlight_start":1,"highlight_end":15}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":225,"byte_end":239,"line_start":8,"line_end":8,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod state;","highlight_start":1,"highlight_end":15}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\entities\\mod.rs","byte_start":240,"byte_end":254,"line_start":9,"line_end":9,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod event;","highlight_start":1,"highlight_end":15}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":246,"byte_end":269,"line_start":8,"line_end":8,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"pub mod component_type;","highlight_start":1,"highlight_end":24}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":270,"byte_end":284,"line_start":9,"line_end":9,"column_start":1,"column_end":15,"is_primary":true,"text":[{"text":"pub mod style;","highlight_start":1,"highlight_end":15}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":285,"byte_end":300,"line_start":10,"line_end":10,"column_start":1,"column_end":16,"is_primary":true,"text":[{"text":"pub mod layout;","highlight_start":1,"highlight_end":16}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\value_objects\\mod.rs","byte_start":301,"byte_end":320,"line_start":11,"line_end":11,"column_start":1,"column_end":20,"is_primary":true,"text":[{"text":"pub mod dimensions;","highlight_start":1,"highlight_end":20}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":587,"byte_end":604,"line_start":14,"line_end":14,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"pub mod services;","highlight_start":1,"highlight_end":18}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\domain\\mod.rs","byte_start":605,"byte_end":626,"line_start":15,"line_end":15,"column_start":1,"column_end":22,"is_primary":true,"text":[{"text":"pub mod repositories;","highlight_start":1,"highlight_end":22}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\lib.rs","byte_start":816,"byte_end":839,"line_start":24,"line_end":24,"column_start":1,"column_end":24,"is_primary":true,"text":[{"text":"pub mod infrastructure;","highlight_start":1,"highlight_end":24}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"crates\\core\\src\\lib.rs","byte_start":841,"byte_end":862,"line_start":25,"line_end":25,"column_start":1,"column_end":22,"is_primary":true,"text":[{"text":"pub mod presentation;","highlight_start":1,"highlight_end":22}],"label":"loaded again here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"replace all but one `mod` item with `use` items","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#duplicate_mod","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the lint level is defined here","code":null,"level":"note","spans":[{"file_name":"crates\\core\\src\\lib.rs","byte_start":714,"byte_end":725,"line_start":19,"line_end":19,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"#![deny(clippy::all)]","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"`#[deny(clippy::duplicate_mod)]` implied by `#[deny(clippy::all)]`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: file is loaded as a module multiple times: `crates\\core\\src\\lib.rs`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:22:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod application;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mfirst loaded here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod domain;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod infrastructure;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod presentation;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mcrates\\core\\src\\domain\\entities\\mod.rs:7:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod props;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod state;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod event;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\mod.rs:8:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod component_type;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod style;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod layout;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod dimensions;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mcrates\\core\\src\\domain\\mod.rs:14:1\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod services;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub mod repositories;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mloaded again here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: replace all but one `mod` item with `use` items\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#duplicate_mod\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: the lint level is defined here\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\lib.rs:19:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#![deny(clippy::all)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[deny(clippy::duplicate_mod)]` implied by `#[deny(clippy::all)]`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"failed to resolve: use of unresolved module or unlinked crate `fastrand`","code":{"code":"E0433","explanation":"An undeclared crate, module, or type was used.\n\nErroneous code example:\n\n```compile_fail,E0433\nlet map = HashMap::new();\n// error: failed to resolve: use of undeclared type `HashMap`\n```\n\nPlease verify you didn't misspell the type/module's name or that you didn't\nforget to import it:\n\n```\nuse std::collections::HashMap; // HashMap has been imported.\nlet map: HashMap<u32, u32> = HashMap::new(); // So it can be used!\n```\n\nIf you've expected to use a crate name:\n\n```compile_fail\nuse ferris_wheel::BigO;\n// error: failed to resolve: use of undeclared module or unlinked crate\n```\n\nMake sure the crate has been added as a dependency in `Cargo.toml`.\n\nTo use a module from your current crate, add the `crate::` prefix to the path.\n"},"level":"error","spans":[{"file_name":"crates\\core\\src\\domain\\value_objects\\component_id.rs","byte_start":2443,"byte_end":2451,"line_start":74,"line_end":74,"column_start":22,"column_end":30,"is_primary":true,"text":[{"text":"        let random = fastrand::u64(..);","highlight_start":22,"highlight_end":30}],"label":"use of unresolved module or unlinked crate `fastrand`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if you wanted to use a crate named `fastrand`, use `cargo add fastrand` to add it to your `Cargo.toml`","code":null,"level":"help","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0433]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: failed to resolve: use of unresolved module or unlinked crate `fastrand`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcrates\\core\\src\\domain\\value_objects\\component_id.rs:74:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let random = fastrand::u64(..);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9muse of unresolved module or unlinked crate `fastrand`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: if you wanted to use a crate named `fastrand`, use `cargo add fastrand` to add it to your `Cargo.toml`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 15 previous errors; 12 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 15 previous errors; 12 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0432, E0433, E0583.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0432, E0433, E0583.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0432`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0432`.\u001b[0m\n"}
