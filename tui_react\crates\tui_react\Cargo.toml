[package]
name = "tui_react"
version = "0.1.0"
edition = "2024"
authors = ["Your Name <<EMAIL>>"]
description = "A React-like UI library for Ratatui with declarative components and JSX-like syntax"
license = "MIT OR Apache-2.0"
repository = "https://github.com/yourusername/ratatui-react"
keywords = ["tui", "terminal", "ui", "react", "ratatui"]
categories = ["command-line-interface", "gui"]

[lib]
name = "tui_react"
path = "src/lib.rs"

[dependencies]
# Core dependencies
tui_react_core = { workspace = true }

# Proc macros
rsx_macro = { workspace = true }
