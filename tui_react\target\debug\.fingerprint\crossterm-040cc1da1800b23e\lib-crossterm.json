{"rustc": 16591470773350601817, "features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"events\", \"windows\"]", "declared_features": "[\"bracketed-paste\", \"default\", \"derive-more\", \"event-stream\", \"events\", \"filedescriptor\", \"libc\", \"osc52\", \"serde\", \"use-dev-tty\", \"windows\"]", "target": 7162149947039624270, "profile": 15657897354478470176, "path": 5746503555594741356, "deps": [[4495526598637097934, "parking_lot", false, 13173721240418322480], [7896293946984509699, "bitflags", false, 17998285461545652388], [10020888071089587331, "<PERSON>ap<PERSON>", false, 3425501153002103855], [11293676373856528358, "derive_more", false, 8221377057756594096], [11763018104473073732, "document_features", false, 13927845093918081132], [17658759660230624279, "crossterm_winapi", false, 5853316572389965229]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\crossterm-040cc1da1800b23e\\dep-lib-crossterm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}