//! Core functionality for TUI React
//!
//! This crate provides the fundamental types and utilities used throughout
//! the TUI React ecosystem.

#![deny(missing_docs)]
#![deny(clippy::all)]

pub mod component;
pub mod hooks;
pub mod render;

pub use component::*;
pub use hooks::*;
pub use render::*;

/// Re-export commonly used types from ratatui
pub use ratatui::{
    Frame, Terminal,
    backend::Backend,
    layout::{Constraint, Direction, Layout, Rect},
    style::{Color, Modifier, Style},
    text::{Line, Span, Text as TuiText},
    widgets::{Block, Borders, Clear, Paragraph, Widget},
};
